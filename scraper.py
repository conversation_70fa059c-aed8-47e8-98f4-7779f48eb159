import json
import time
from datetime import datetime
from pathlib import Path
from playwright.sync_api import sync_playwright

OUTPUT = Path("matches.json")

def scrape_matches():
    data = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        page.goto("https://www.flashscore.com/tennis/")
        page.wait_for_selector("text=LIVE")
        page.click("text=LIVE")
        time.sleep(2)

        for m in page.query_selector_all("div.event__match"):
            try:
                players = m.query_selector_all(".event__participant")
                p1 = players[0].inner_text().strip()
                p2 = players[1].inner_text().strip()
                sets = [s.inner_text().strip() for s in m.query_selector_all(".event__scores")][:2]
                server_el = m.query_selector(".event__stage--live")
                current_server = server_el.inner_text().strip() if server_el else None
                data.append({
                    "timestamp": datetime.utcnow().isoformat() + "Z",
                    "player1": p1,
                    "player2": p2,
                    "sets": sets,
                    "current_server": current_server
                })
            except:
                pass

        browser.close()
    return data

def main():
    while True:
        result = scrape_matches()
        OUTPUT.write_text(json.dumps(result, indent=2, ensure_ascii=False))
        print(f"Saved {len(result)} match entries at {datetime.utcnow().isoformat()}Z")
        time.sleep(10)

if __name__ == "__main__":
    main()
