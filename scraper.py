import json
import time
from datetime import datetime, timezone
from pathlib import Path
from playwright.sync_api import sync_playwright

OUTPUT = Path("data/matches.json")

def scrape_matches():
    data = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()
        page.goto("https://www.flashscore.com/tennis/")
        page.wait_for_selector("text=LIVE")
        page.click("text=LIVE")
        time.sleep(3)

        matches = page.query_selector_all("div.event__match")
        print(f"Found {len(matches)} matches")

        for i, m in enumerate(matches):
            try:
                # Extrair jogadores
                players = m.query_selector_all(".event__participant")
                if len(players) < 2:
                    continue

                p1 = players[0].inner_text().strip()
                p2 = players[1].inner_text().strip()

                # Tentar diferentes seletores para os placares
                scores = []

                # Tentar seletor principal para placares
                score_elements = m.query_selector_all(".event__score")
                if score_elements:
                    scores = [s.inner_text().strip() for s in score_elements if s.inner_text().strip()]

                # Se não encontrou, tentar outros seletores
                if not scores:
                    score_elements = m.query_selector_all(".event__scores")
                    if score_elements:
                        scores = [s.inner_text().strip() for s in score_elements if s.inner_text().strip()]

                # Tentar seletor para placares individuais
                if not scores:
                    score_elements = m.query_selector_all(".event__part")
                    if score_elements:
                        scores = [s.inner_text().strip() for s in score_elements if s.inner_text().strip()]

                # Extrair sets individuais
                sets_data = []
                set_elements = m.query_selector_all(".event__part--home, .event__part--away")
                if set_elements:
                    # Agrupar em pares (home, away)
                    for j in range(0, len(set_elements), 2):
                        if j + 1 < len(set_elements):
                            home_score = set_elements[j].inner_text().strip()
                            away_score = set_elements[j + 1].inner_text().strip()
                            if home_score and away_score:
                                sets_data.append(f"{home_score}-{away_score}")

                # Status da partida
                status_el = m.query_selector(".event__stage")
                status = status_el.inner_text().strip() if status_el else None

                # Servidor atual
                server_el = m.query_selector(".event__stage--live")
                current_server = server_el.inner_text().strip() if server_el else None

                # Placar atual
                current_score = None
                current_score_el = m.query_selector(".event__score--home, .event__score--away")
                if current_score_el:
                    current_score = current_score_el.inner_text().strip()

                match_data = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "player1": p1,
                    "player2": p2,
                    "sets": sets_data,
                    "scores": scores,
                    "current_score": current_score,
                    "status": status,
                    "current_server": current_server
                }

                data.append(match_data)

                # Debug para as primeiras partidas
                if i < 3:
                    print(f"Match {i+1}: {p1} vs {p2}")
                    print(f"  Sets: {sets_data}")
                    print(f"  Scores: {scores}")
                    print(f"  Status: {status}")

            except Exception as e:
                print(f"Error processing match {i}: {e}")
                continue

        browser.close()
    return data

def main():
    while True:
        result = scrape_matches()
        OUTPUT.write_text(json.dumps(result, indent=2, ensure_ascii=False))
        print(f"Saved {len(result)} match entries at {datetime.now(timezone.utc).isoformat()}")
        time.sleep(10)

if __name__ == "__main__":
    main()
