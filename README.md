# Tennis Data Scraper

Este projeto contém um scraper de dados de tênis que coleta informações de partidas ao vivo do Flashscore usando Docker.

## Estrutura do Projeto

```
tennis-data/
├── scraper.py          # Script principal do scraper
├── requirements.txt    # Dependências Python
├── Dockerfile         # Configuração da imagem Docker
├── docker-compose.yml # Configuração do Docker Compose
├── .dockerignore      # Arquivos ignorados no build
├── data/              # Diretório para dados coletados
└── README.md          # Este arquivo
```

## Como Usar

### Opção 1: Docker Compose (Recomendado)

1. **Construir e executar o container:**
   ```bash
   docker-compose up --build
   ```

2. **Executar em background:**
   ```bash
   docker-compose up -d --build
   ```

3. **Ver logs:**
   ```bash
   docker-compose logs -f tennis-scraper
   ```

4. **Parar o container:**
   ```bash
   docker-compose down
   ```

### Opção 2: Docker Manual

1. **Construir a imagem:**
   ```bash
   docker build -t tennis-scraper .
   ```

2. **Executar o container:**
   ```bash
   docker run -v $(pwd)/data:/app/data tennis-scraper
   ```

## Funcionalidades

- Coleta dados de partidas de tênis ao vivo do Flashscore
- Salva os dados em formato JSON no arquivo `data/matches.json`
- Atualiza os dados a cada 10 segundos
- Executa continuamente até ser interrompido

## Dados Coletados

Para cada partida, o scraper coleta:
- Timestamp da coleta
- Nome dos jogadores (player1, player2)
- Placar dos sets
- Servidor atual (quando disponível)

## Requisitos

- Docker
- Docker Compose (opcional, mas recomendado)

## Notas

- O arquivo `matches.json` será criado no diretório `data/`
- Os dados são persistidos mesmo quando o container é reiniciado
- O scraper usa Playwright com Chromium em modo headless
