import json
import time
from datetime import datetime, timezone
from pathlib import Path
from playwright.sync_api import sync_playwright

def test_scrape():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)  # Não headless para debug
        page = browser.new_page()
        page.goto("https://www.flashscore.com/tennis/")
        
        # Aguardar a página carregar
        page.wait_for_selector("text=LIVE", timeout=10000)
        page.click("text=LIVE")
        time.sleep(3)
        
        # Primeiro, vamos inspecionar a estrutura da página
        print("=== INSPECIONANDO ESTRUTURA DA PÁGINA ===")
        
        # Verificar se existem partidas
        matches = page.query_selector_all("div.event__match")
        print(f"Encontradas {len(matches)} partidas")
        
        if len(matches) > 0:
            # Analisar a primeira partida em detalhes
            first_match = matches[0]
            print("\n=== PRIMEIRA PARTIDA ===")
            
            # Extrair HTML da primeira partida para análise
            match_html = first_match.inner_html()
            print("HTML da partida:")
            print(match_html[:500] + "..." if len(match_html) > 500 else match_html)
            
            # Tentar diferentes seletores
            print("\n=== TESTANDO SELETORES ===")
            
            # Jogadores
            players = first_match.query_selector_all(".event__participant")
            print(f"Jogadores encontrados: {len(players)}")
            for i, player in enumerate(players):
                print(f"  Jogador {i+1}: {player.inner_text().strip()}")
            
            # Tentar diferentes seletores para placares
            selectors_to_try = [
                ".event__score",
                ".event__scores", 
                ".event__part",
                ".event__part--home",
                ".event__part--away",
                "[class*='score']",
                "[class*='part']"
            ]
            
            for selector in selectors_to_try:
                elements = first_match.query_selector_all(selector)
                print(f"Seletor '{selector}': {len(elements)} elementos")
                for i, elem in enumerate(elements[:5]):  # Mostrar apenas os primeiros 5
                    text = elem.inner_text().strip()
                    if text:
                        print(f"  {i+1}: '{text}'")
            
            # Verificar status da partida
            status_selectors = [
                ".event__stage",
                ".event__stage--live",
                "[class*='stage']"
            ]
            
            for selector in status_selectors:
                elem = first_match.query_selector(selector)
                if elem:
                    print(f"Status ({selector}): '{elem.inner_text().strip()}'")
        
        # Aguardar um pouco para poder ver a página
        time.sleep(5)
        browser.close()

if __name__ == "__main__":
    test_scrape()
